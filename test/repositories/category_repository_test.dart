import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:objectid/objectid.dart';
import 'package:pocket_trac/extension.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/providers/box_provider.dart';
import 'package:pocket_trac/app/repositories/category_repository.dart';

void main() {
  group('CategoryRepository Unit Tests', () {
    late CategoryRepository categoryRepository;
    late BoxProvider boxProvider;
    late Talker talker;

    setUp(() {
      talker = TalkerFlutter.init();
      boxProvider = BoxProvider(talker: talker);
      categoryRepository = CategoryRepository(boxProvider);
    });

    group('Constructor and Basic Properties', () {
      test('should create CategoryRepository with BoxProvider', () {
        // Assert
        expect(categoryRepository.boxProvider, equals(boxProvider));
        expect(categoryRepository.talker, equals(talker));
      });

      test('should have access to talker through boxProvider', () {
        // Assert
        expect(categoryRepository.talker, isA<Talker>());
        expect(categoryRepository.talker, equals(boxProvider.talker));
      });
    });

    group('ErpCategory Model Tests', () {
      test('should create ErpCategory with correct properties', () {
        // Arrange & Act
        final category = ErpCategory(
          name: '測試分類',
          color: '#FF0000',
          icon: 'test_icon',
        );

        // Assert
        expect(category.name, equals('測試分類'));
        expect(category.color, equals('#FF0000'));
        expect(category.icon, equals('test_icon'));
        expect(category.id, isNull);
        expect(category.createdAt, isNull);
        expect(category.updatedAt, isNull);
        expect(category.deletedAt, isNull);
        expect(category.objectId, isNull);
      });

      test('should copy category with new values', () {
        // Arrange
        final original = ErpCategory(
          name: '原始分類',
          color: '#FF0000',
          icon: 'original_icon',
        );

        // Act
        final copied = original.copyWith(
          name: '新分類',
          color: '#00FF00',
        );

        // Assert
        expect(copied.name, equals('新分類'));
        expect(copied.color, equals('#00FF00'));
        expect(copied.icon, equals('original_icon')); // Should keep original value
        expect(copied.id, equals(original.id));
      });

      test('should convert to and from JSON correctly', () {
        // Arrange
        final now = DateTime.now();
        final category = ErpCategory(
          id: 1,
          name: '測試分類',
          color: '#FF0000',
          icon: 'test_icon',
          createdAt: now,
          updatedAt: now,
          objectId: ObjectId().hexString,
        );

        // Act
        final json = category.toJson();
        final fromJson = ErpCategory.fromJson(json);

        // Assert
        expect(fromJson.id, equals(category.id));
        expect(fromJson.name, equals(category.name));
        expect(fromJson.color, equals(category.color));
        expect(fromJson.icon, equals(category.icon));
        expect(fromJson.objectId, equals(category.objectId));
        expect(fromJson.createdAt?.millisecondsSinceEpoch,
               equals(category.createdAt?.millisecondsSinceEpoch));
        expect(fromJson.updatedAt?.millisecondsSinceEpoch,
               equals(category.updatedAt?.millisecondsSinceEpoch));
      });

      test('should handle null values in JSON conversion', () {
        // Arrange
        final category = ErpCategory(name: '測試分類');

        // Act
        final json = category.toJson();
        final fromJson = ErpCategory.fromJson(json);

        // Assert
        expect(fromJson.name, equals('測試分類'));
        expect(fromJson.id, isNull);
        expect(fromJson.color, isNull);
        expect(fromJson.icon, isNull);
        expect(fromJson.createdAt, isNull);
        expect(fromJson.updatedAt, isNull);
        expect(fromJson.deletedAt, isNull);
        expect(fromJson.objectId, isNull);
      });

      test('should get correct color from color string', () {
        // Arrange
        final category1 = ErpCategory(color: '#FF0000');
        final category2 = ErpCategory(color: null);
        final category3 = ErpCategory(color: '');
        final category4 = ErpCategory(color: 'invalid');

        // Act & Assert
        expect(category1.getColor().value, equals(0xFFFF0000));
        expect(category2.getColor(), isA<Color>()); // Should return default color
        expect(category3.getColor(), isA<Color>()); // Should return default color
        expect(category4.getColor(), isA<Color>()); // Should return default color
      });
    });

    group('ObjectId Tests', () {
      test('should generate valid ObjectId', () {
        // Act
        final objectId = ObjectId().hexString;

        // Assert
        expect(objectId, isNotNull);
        expect(objectId.length, equals(24));
        expect(RegExp(r'^[0-9a-fA-F]{24}$').hasMatch(objectId), isTrue);
      });

      test('should generate unique ObjectIds', () {
        // Act
        final objectId1 = ObjectId().hexString;
        final objectId2 = ObjectId().hexString;

        // Assert
        expect(objectId1, isNot(equals(objectId2)));
      });
    });
  });
}
