import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pocket_trac/app/models/erp_category.dart';
import 'package:pocket_trac/app/widgets/category_item.dart';

void main() {
  group('CategoryItem Widget Tests', () {
    testWidgets('should display category information correctly', (WidgetTester tester) async {
      // 创建测试数据
      final category = ErpCategory(
        name: '餐飲',
        color: '#FF0000',
      );
      category.amount = 1500;

      bool tapped = false;

      // 构建 widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CategoryItem(
              category: category,
              onTap: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      // 验证分类名称显示
      expect(find.text('餐飲'), findsOneWidget);
      
      // 验证交易信息显示
      expect(find.textContaining('筆交易'), findsOneWidget);
      expect(find.textContaining('本月'), findsOneWidget);

      // 验证点击功能
      await tester.tap(find.byType(CategoryItem));
      expect(tapped, isTrue);
    });

    testWidgets('should handle null category name', (WidgetTester tester) async {
      // 创建测试数据
      final category = ErpCategory(
        name: null,
        color: '#FF0000',
      );

      // 构建 widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CategoryItem(
              category: category,
            ),
          ),
        ),
      );

      // 验证默认名称显示
      expect(find.text('未知分類'), findsOneWidget);
    });

    testWidgets('should display correct amount formatting', (WidgetTester tester) async {
      // 创建测试数据
      final category = ErpCategory(
        name: '測試分類',
        color: '#FF0000',
      );
      category.amount = 0;

      // 构建 widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CategoryItem(
              category: category,
            ),
          ),
        ),
      );

      // 验证零金额显示
      expect(find.textContaining('\$0'), findsOneWidget);
    });
  });
}
