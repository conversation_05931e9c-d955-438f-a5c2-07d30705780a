import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SheetWrapper extends StatelessWidget {
  final Widget child;
  final VoidCallback? onInit;
  final String title;

  const SheetWrapper({
    super.key,
    required this.child,
    this.onInit,
    this.title = '',
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 執行初始化回調
        onInit?.call();
        return DraggableScrollableSheet(
          initialChildSize: 0.9, // 初始高度為屏幕的90%
          minChildSize: 0.5, // 最小高度為屏幕的50%
          maxChildSize: 0.9, // 最大高度為屏幕的90%
          expand: false, // 不要讓子元素擴展到整個可滾動區域
          builder: (context, scrollController) {
            return _buildBody();
          },
        );
      },
    );
  }

  Widget _buildBody() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: getChildren().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> getChildren() sync* {
    // 拖拽指示器
    yield Container(
      margin: const EdgeInsets.only(top: 8),
      height: 4,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
    // 標題欄
    yield Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB)),
        ),
      ),
      child: _buildHeader(),
    );
    // 內容區域
    yield Flexible(child: child);
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.close),
          style: IconButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: const Size(24, 24),
          ),
        ),
      ],
    );
  }
}
