# CategoryRepository 串流功能使用指南

本指南介紹如何使用 `CategoryRepository` 中新增的響應式串流功能。這些串流功能基於 ObjectBox 的響應式查詢，能夠自動監聽資料庫變更並即時更新 UI。

## 功能概覽

CategoryRepository 提供了以下串流功能：

1. **watchAll()** - 監聽所有類別變更（包括已刪除的）
2. **watchActive()** - 監聽未刪除類別變更
3. **watchByName(String name)** - 監聽特定名稱搜尋結果
4. **watchCount({bool includeDeleted})** - 監聽類別數量變更
5. **watchById(int id)** - 監聽特定 ID 類別變更
6. **watchByObjectId(String objectId)** - 監聽特定 ObjectId 類別變更
7. **watchDeleted()** - 監聽已刪除類別變更

## 基本使用方法

### 1. 監聽所有活躍類別

```dart
final categoryRepository = Get.find<CategoryRepository>();

// 監聽所有未刪除的類別
StreamSubscription<List<ErpCategory>>? subscription;

subscription = categoryRepository.watchActive().listen(
  (categories) {
    print('收到 ${categories.length} 個類別');
    // 更新 UI
    updateCategoryList(categories);
  },
  onError: (error) {
    print('串流錯誤: $error');
  },
);

// 記得在不需要時取消訂閱
subscription?.cancel();
```

### 2. 監聽類別數量變更

```dart
// 監聽活躍類別數量
categoryRepository.watchCount().listen(
  (count) {
    print('目前有 $count 個活躍類別');
    updateCategoryCount(count);
  },
);

// 監聽所有類別數量（包括已刪除的）
categoryRepository.watchCount(includeDeleted: true).listen(
  (count) {
    print('總共有 $count 個類別');
  },
);
```

### 3. 監聽特定類別變更

```dart
// 監聽特定 ID 的類別
categoryRepository.watchById(1).listen(
  (category) {
    if (category != null) {
      print('類別已更新: ${category.name}');
      updateCategoryDetail(category);
    } else {
      print('類別已被刪除或不存在');
      showCategoryNotFound();
    }
  },
);

// 監聽特定 ObjectId 的類別
categoryRepository.watchByObjectId('507f1f77bcf86cd799439011').listen(
  (category) {
    // 處理類別變更
  },
);
```

### 4. 搜尋功能的響應式實現

```dart
// 監聽搜尋結果
String searchQuery = '食物';
categoryRepository.watchByName(searchQuery).listen(
  (searchResults) {
    print('找到 ${searchResults.length} 個符合 "$searchQuery" 的類別');
    updateSearchResults(searchResults);
  },
);
```

## 在 Flutter Widget 中使用

### 使用 StreamBuilder

```dart
class CategoryListWidget extends StatelessWidget {
  final CategoryRepository categoryRepository = Get.find();

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ErpCategory>>(
      stream: categoryRepository.watchActive(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text('錯誤: ${snapshot.error}');
        }
        
        if (!snapshot.hasData) {
          return CircularProgressIndicator();
        }
        
        final categories = snapshot.data!;
        return ListView.builder(
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return ListTile(
              title: Text(category.name ?? ''),
              subtitle: Text('ID: ${category.id}'),
              leading: Icon(category.getIcon()),
              trailing: Container(
                width: 20,
                height: 20,
                color: category.getColor(),
              ),
            );
          },
        );
      },
    );
  }
}
```

### 使用 GetX 響應式

```dart
class CategoryController extends GetxController {
  final CategoryRepository categoryRepository = Get.find();
  
  final categories = <ErpCategory>[].obs;
  final categoryCount = 0.obs;
  
  StreamSubscription<List<ErpCategory>>? _categoriesSubscription;
  StreamSubscription<int>? _countSubscription;
  
  @override
  void onInit() {
    super.onInit();
    
    // 監聽類別列表變更
    _categoriesSubscription = categoryRepository.watchActive().listen(
      (newCategories) {
        categories.value = newCategories;
      },
    );
    
    // 監聽類別數量變更
    _countSubscription = categoryRepository.watchCount().listen(
      (newCount) {
        categoryCount.value = newCount;
      },
    );
  }
  
  @override
  void onClose() {
    _categoriesSubscription?.cancel();
    _countSubscription?.cancel();
    super.onClose();
  }
}

// 在 Widget 中使用
class CategoryView extends GetView<CategoryController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text('類別 (${controller.categoryCount})')),
      ),
      body: Obx(() {
        return ListView.builder(
          itemCount: controller.categories.length,
          itemBuilder: (context, index) {
            final category = controller.categories[index];
            return CategoryTile(category: category);
          },
        );
      }),
    );
  }
}
```

## 搜尋功能實現

```dart
class CategorySearchController extends GetxController {
  final CategoryRepository categoryRepository = Get.find();
  
  final searchQuery = ''.obs;
  final searchResults = <ErpCategory>[].obs;
  
  StreamSubscription<List<ErpCategory>>? _searchSubscription;
  
  void updateSearchQuery(String query) {
    searchQuery.value = query;
    
    // 取消之前的搜尋訂閱
    _searchSubscription?.cancel();
    
    if (query.isNotEmpty) {
      // 開始新的搜尋監聽
      _searchSubscription = categoryRepository.watchByName(query).listen(
        (results) {
          searchResults.value = results;
        },
      );
    } else {
      searchResults.clear();
    }
  }
  
  @override
  void onClose() {
    _searchSubscription?.cancel();
    super.onClose();
  }
}
```

## 最佳實踐

### 1. 記憶體管理

```dart
class MyController extends GetxController {
  final List<StreamSubscription> _subscriptions = [];
  
  @override
  void onInit() {
    super.onInit();
    
    // 將所有訂閱加入列表
    _subscriptions.add(
      categoryRepository.watchActive().listen((categories) {
        // 處理類別變更
      }),
    );
  }
  
  @override
  void onClose() {
    // 取消所有訂閱
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    super.onClose();
  }
}
```

### 2. 錯誤處理

```dart
categoryRepository.watchActive().listen(
  (categories) {
    // 處理正常資料
    updateUI(categories);
  },
  onError: (error, stackTrace) {
    // 處理錯誤
    talker.error('類別串流錯誤: $error', error, stackTrace);
    showErrorMessage('載入類別時發生錯誤');
  },
);
```

### 3. 條件性監聽

```dart
// 只在特定條件下監聽
if (shouldWatchCategories) {
  subscription = categoryRepository.watchActive().listen((categories) {
    // 處理類別變更
  });
}
```

## 性能考量

1. **適時取消訂閱**: 在 Widget dispose 或 Controller close 時取消訂閱
2. **避免重複訂閱**: 確保不會對同一個查詢創建多個訂閱
3. **使用適當的串流**: 根據需求選擇最合適的串流方法
4. **錯誤處理**: 總是提供錯誤處理邏輯

## 注意事項

1. 所有串流都設置了 `triggerImmediately: true`，會在訂閱時立即發出當前資料
2. 串流會自動處理資料庫事務邊界，確保資料一致性
3. 錯誤會通過 Talker 記錄，同時也會傳播給串流監聽者
4. 串流是基於 ObjectBox 的響應式查詢，性能優異且資源消耗低
