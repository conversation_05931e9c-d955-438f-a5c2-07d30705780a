# CategoryRepository 異步模式指南

## 概述

我們為 `CategoryRepository` 添加了完整的異步支持，提供了所有同步方法的異步版本。這些異步方法提供了更好的性能和用戶體驗，特別是在處理大量數據時。

## 新增的異步方法

### 查詢方法

| 同步方法 | 異步方法 | 說明 |
|---------|---------|------|
| `getAll()` | `getAllAsync()` | 獲取所有類別 |
| `getById()` | `getByIdAsync()` | 按ID獲取類別 |
| `getByUuid()` | `getByUuidAsync()` | 按UUID獲取類別 |
| `searchByName()` | `searchByNameAsync()` | 按名稱搜索類別 |
| `getAllSorted()` | `getAllSortedAsync()` | 獲取排序後的類別列表 |
| `count()` | `countAsync()` | 獲取類別總數 |

### 修改方法

| 同步方法 | 異步方法 | 說明 |
|---------|---------|------|
| `add()` | `addAsync()` | 新增類別 |
| `update()` | `updateAsync()` | 更新類別 |
| `putMany()` | `putManyAsync()` | 批量添加或更新類別 |
| `softDelete()` | `softDeleteAsync()` | 軟刪除類別 |
| `restore()` | `restoreAsync()` | 恢復已軟刪除的類別 |
| `hardDelete()` | `hardDeleteAsync()` | 硬刪除類別 |
| `deleteAll()` | `deleteAllAsync()` | 刪除所有類別 |

## 主要特性

### 1. 錯誤處理
所有異步方法都包含完整的錯誤處理：
- 使用 try-catch 包裝所有操作
- 通過 Talker 記錄詳細的錯誤日誌
- 重新拋出異常以便上層處理

### 2. 日誌記錄
集成了 Talker 日誌系統：
- 記錄操作成功和失敗的詳細信息
- 包含堆棧跟踪信息
- 便於調試和監控

### 3. 向後兼容
- 保留所有原有的同步方法
- 不影響現有代碼的功能
- 可以逐步遷移到異步版本

## 使用示例

### 基本用法

```dart
// 初始化 repository
final boxProvider = Get.find<BoxProvider>();
final talker = boxProvider.talker;
final repository = CategoryRepository(boxProvider.store, talker: talker);

// 異步獲取所有類別
try {
  final categories = await repository.getAllAsync();
  print('Found ${categories.length} categories');
} catch (e) {
  print('Error loading categories: $e');
}

// 異步添加新類別
try {
  final category = ErpCategory(
    name: '新類別',
    color: '#FF0000',
    sort: 1,
  );
  final id = await repository.addAsync(category);
  print('Category added with ID: $id');
} catch (e) {
  print('Error adding category: $e');
}
```

### 在 GetX Controller 中使用

```dart
class CategoriesController extends GetxController {
  late final CategoryRepository _categoryRepository;
  final categories = <ErpCategory>[].obs;
  final isLoading = false.obs;

  Future<void> loadCategories() async {
    try {
      isLoading.value = true;
      final categoryList = await _categoryRepository.getAllSortedAsync();
      categories.value = categoryList;
    } catch (e) {
      // 處理錯誤
    } finally {
      isLoading.value = false;
    }
  }
}
```

## 性能優勢

### 1. 非阻塞操作
- 異步方法不會阻塞 UI 線程
- 提供更流暢的用戶體驗
- 適合處理大量數據

### 2. 並發支持
- 可以同時執行多個異步操作
- 提高應用程序的響應性
- 更好的資源利用率

### 3. 內存效率
- ObjectBox 的異步 API 更高效
- 減少內存佔用
- 更好的垃圾回收性能

## 最佳實踐

### 1. 錯誤處理
```dart
try {
  final result = await repository.someAsyncMethod();
  // 處理成功結果
} catch (e, s) {
  // 記錄錯誤
  talker.error('Operation failed: $e', e, s);
  // 向用戶顯示友好的錯誤消息
  showErrorMessage('操作失敗，請稍後重試');
}
```

### 2. 加載狀態管理
```dart
Future<void> performOperation() async {
  isLoading.value = true;
  try {
    // 執行異步操作
    await repository.someAsyncMethod();
  } finally {
    isLoading.value = false;
  }
}
```

### 3. 批量操作
```dart
// 使用批量方法而不是循環調用單個方法
final categories = [/* ... */];
await repository.putManyAsync(categories);
```

## 遷移指南

### 從同步到異步的遷移步驟：

1. **識別需要遷移的方法**
   - 查找所有使用同步方法的地方
   - 優先遷移耗時較長的操作

2. **更新方法調用**
   ```dart
   // 舊版本
   final categories = repository.getAll();
   
   // 新版本
   final categories = await repository.getAllAsync();
   ```

3. **添加錯誤處理**
   ```dart
   try {
     final categories = await repository.getAllAsync();
     // 處理結果
   } catch (e) {
     // 處理錯誤
   }
   ```

4. **更新方法簽名**
   ```dart
   // 舊版本
   void loadData() {
     final data = repository.getAll();
   }
   
   // 新版本
   Future<void> loadData() async {
     final data = await repository.getAllAsync();
   }
   ```

## 測試

我們提供了完整的測試套件來驗證異步功能：

```bash
flutter test test/repositories/category_repository_async_test.dart
```

測試覆蓋了所有異步方法的功能，包括：
- 基本 CRUD 操作
- 錯誤處理
- 邊界條件
- 性能測試

## 注意事項

1. **版本兼容性**：確保 ObjectBox 版本支持所使用的異步 API
2. **錯誤處理**：始終包含適當的錯誤處理邏輯
3. **性能監控**：監控異步操作的性能，特別是在大數據集上
4. **測試**：為使用異步方法的代碼編寫適當的測試

## 總結

異步模式的添加為 CategoryRepository 帶來了顯著的性能提升和更好的用戶體驗。通過遵循本指南中的最佳實踐，您可以充分利用這些新功能，同時保持代碼的可維護性和可靠性。
